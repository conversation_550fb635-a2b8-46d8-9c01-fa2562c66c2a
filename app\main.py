from fastapi import <PERSON><PERSON><PERSON>, WebSocket, Request
from fastapi.middleware.cors import CORSMiddleware
from fastapi.staticfiles import StaticFiles
from fastapi.responses import FileResponse
from fastapi.websockets import WebSocketDisconnect

from app.config import get_settings
from app.core.progress import ProgressBus
from app.routers import inpaint


def create_app() -> FastAPI:
    app = FastAPI(
        title="ComfyUI Inpainting Backend", 
        version="0.1.0",
        description="""
        ## ComfyUI Inpainting Backend API
        
        This API provides endpoints for:
        - **Inpainting workflows** - Submit images for AI-powered inpainting
        - **Progress tracking** - Real-time WebSocket progress updates  
        - **Result retrieval** - Download generated images
        
        ### Quick Start:
        1. Use `/api/inpaint/run` to submit a workflow
        2. Connect to `/ws/progress/{prompt_id}` for real-time updates
        3. Download results from `/api/inpaint/result/{prompt_id}`
        """,
        docs_url="/docs",  # Swagger UI
        redoc_url="/redoc"  # ReDoc alternative UI
    )

    # CORS (adjust as needed)
    app.add_middleware(
        CORSMiddleware,
        allow_origins=["*"],
        allow_credentials=True,
        allow_methods=["*"],
        allow_headers=["*"],
    )

    # App state
    settings = get_settings()
    app.state.progress_bus = ProgressBus(max_queue_size=settings.PROGRESS_QUEUE_SIZE)
    app.state.run_index = {}  # prompt_id -> server_address

    # Routers
    app.include_router(inpaint.router)

    # Static files for UI
    try:
        app.mount("/static", StaticFiles(directory="static"), name="static")
    except RuntimeError:
        pass  # Directory might not exist yet

    # Serve the test UI at root
    @app.get("/")
    async def serve_ui():
        try:
            return FileResponse("static/index.html")
        except FileNotFoundError:
            return {"message": "UI not found. Visit /docs for Swagger UI instead."}

    # WebSocket for progress
    @app.websocket("/ws/progress/{prompt_id}")
    async def ws_progress(websocket: WebSocket, prompt_id: str):
        # Access app state directly instead of through request
        bus = app.state.progress_bus
        await websocket.accept()
        queue = await bus.subscribe(prompt_id)
        try:
            while True:
                event = await queue.get()
                await websocket.send_json(event)
                if event.get("type") == "completed":
                    break
        except WebSocketDisconnect:
            pass  # Client disconnected
        except Exception as e:
            print(f"WebSocket error: {e}")
        finally:
            await bus.unsubscribe(prompt_id, queue)
            try:
                await websocket.close()
            except Exception:
                pass

    return app


app = create_app()

