# ComfyUI Inpainting Backend – Implementation Plan

## Overview

Build a FastAPI backend that drives a specific ComfyUI inpainting workflow and streams real-time progress updates to clients. The workflow will insert an object ("subject") into a destination scene using a provided scene mask.

Key constraints and targets:

- Only update the three input nodes in the workflow JSON prior to execution:
  - "18" (destination scene image) – class_type LoadImage
  - "37" (object/subject image) – class_type LoadImage
  - "293" (scene mask) – class_type LoadImageMask
- Do not change any other node inputs in the workflow JSON.
- Output node is "296" (SaveImage). The saved image(s) must be returned/downloadable upon completion.
- Use uv exclusively for dependency management and running the server.
- Implement real-time progress updates during workflow execution.

Inputs are stored in the repository under /input (relative to the backend working directory) and will be uploaded to the ComfyUI server before queuing the prompt.

## Architecture

- FastAPI app with three layers:

  1. API layer (routers): HTTP + WebSocket endpoints for starting runs, streaming progress, and fetching results.
  2. Service layer (ComfyClient): Encapsulates ComfyUI interactions (upload, queue, progress, history, image fetch) and workflow patching.
  3. Config layer: Centralizes environment/config (ComfyUI host/port, paths, timeouts).

- Runtime data flow:
  1. Client POSTs to /api/inpaint/run with three filenames (relative to /input) or with multipart uploads (optional extension).
  2. Backend uploads files to ComfyUI (/upload/image and /upload/mask).
  3. Backend loads workflow JSON (Subject Destination API.json), updates nodes 18, 37, 293 with those filenames, and posts the prompt to ComfyUI /prompt with a generated client_id.
  4. Backend opens a WebSocket to ComfyUI /ws?clientId=<client_id> and starts listening. Progress messages are forwarded to clients connected to our own WebSocket at /ws/progress/{prompt_id}.
  5. When ComfyUI signals completion for the prompt_id, backend queries /history/{prompt_id}, locates output(s) from node 296, and exposes them via a REST download endpoint.

## API Specification

- Health

  - GET /api/health
  - 200 OK: { "status": "ok" }

- Start Inpainting Run

  - POST /api/inpaint/run
  - Request (JSON):
    - object_image: string (filename in /input, e.g., "object.png") maps to node "37" inputs.image
    - scene_image: string (filename in /input, e.g., "scene.jpg") maps to node "18" inputs.image
    - scene_mask: string (filename in /input, e.g., "scene_mask.png") maps to node "293" inputs.image
    - optional: comfy_server (e.g., "127.0.0.1:8188") to override default
  - Response (JSON):
    - prompt_id: string
    - progress_ws: string (e.g., ws://<host>/ws/progress/{prompt_id})
    - result_url: string (e.g., /api/inpaint/result/{prompt_id})
  - Notes: The server will immediately start streaming progress on the backend → ComfyUI WebSocket and buffer events until at least one client attaches to /ws/progress/{prompt_id}.

- Progress Stream (WebSocket)

  - WS /ws/progress/{prompt_id}
  - Messages forwarded from ComfyUI, e.g.:
    - { "type": "progress", "value": <int>, "max": <int> }
    - { "type": "executing", "node": "<node_id>" }
    - { "type": "execution_cached", "data": { ... } }
    - { "type": "executed", "prompt_id": "..." }
  - Server may also add computed fields (e.g., "percent": value/max) and human-readable node names if available.

- Fetch Result
  - GET /api/inpaint/result/{prompt_id}
  - Behavior: Look up ComfyUI history for prompt_id, find outputs for node "296" (SaveImage), fetch the image(s) via /view, and return the primary image. If multiple images exist, return either the first or a zip (design choice; see Implementation Tasks). Response includes appropriate content-type.

## Real-time Progress Update Mechanism

- For each run, generate a client_id and establish a WebSocket to ComfyUI: ws://{COMFY_HOST}/ws?clientId={client_id}.
- After POSTing the prompt to /prompt and obtaining prompt_id, start an async task to read ComfyUI WebSocket messages.
- Maintain an in-memory registry mapping prompt_id → asyncio.Queue (or Broadcast channel) that receives progress messages.
- The FastAPI WebSocket endpoint /ws/progress/{prompt_id} consumes from the corresponding queue and forwards events to the connected client(s). If no consumers are attached yet, messages are buffered in a bounded queue; if full, drop oldest (configurable) to avoid backpressure.
- Completion detection: when a message of type executed with matching prompt_id is received, mark the run done and close the channel after a final "completed" event.

## Workflow Patching Rules

- Load JSON from Subject Destination API.json.
- Update exactly these nodes:
  - "18".inputs.image = <scene_image_filename>
  - "37".inputs.image = <object_image_filename>
  - "293".inputs.image = <scene_mask_filename>
- Do not alter any other fields. Keep class_type and structure intact.
- Upload the same filenames to ComfyUI before queueing the prompt:
  - /upload/image with type=input for images (object and scene)
  - /upload/mask with type=input for the mask (LoadImageMask node)
- The workflow should then refer to these filenames only (no local paths), as ComfyUI resolves them from its input folder.

## ComfyClient Service – Responsibilities

- Configuration

  - Base URL (http://{host}), WS URL (ws://{host}) – default 127.0.0.1:8188
  - Timeouts, reconnect/backoff strategy (simple initial version)

- Methods

  - connect_ws() → (ws, client_id)
  - upload_file(input_path: Path, filename: str, image_type: "image"|"mask", folder_type: "input" = "input")
  - load_workflow(path: Path) → dict
  - patch_workflow(prompt: dict, scene: str, object: str, mask: str) → dict
  - queue_prompt(prompt: dict, client_id: str) → prompt_id: str
  - track_progress(ws, prompt_id: str, enqueue: Callable[[dict], Awaitable[None]]) → await until completion
  - get_history(prompt_id: str) → dict
  - get_image(filename: str, subfolder: str, folder_type: str) → bytes
  - get_outputs_for_node(prompt_id: str, node_id: str = "296") → List[ImageMeta]

- Implementation notes
  - Use requests for HTTP and websocket-client for WS (consistent with the provided example).
  - Ensure try/finally to close WS connections.
  - Validate HTTP 2xx; raise descriptive exceptions otherwise.

## Step-by-step Implementation Tasks

1. Project setup (uv)

   - From comfyui-backend/, initialize or update project with uv (if not already):
     - uv init --app
     - uv add fastapi --extra standard
     - uv add requests websocket-client python-dotenv pillow
   - Ensure pyproject.toml exists with [project] metadata and dependencies managed by uv.

2. App structure

   - app/
     - main.py – FastAPI app factory and router registration
     - config.py – settings (COMFY_HOST, INPUT_DIR, WORKFLOW_PATH, etc.)
     - services/comfy_client.py – ComfyUI client described above
     - routers/inpaint.py – endpoints: /api/health, /api/inpaint/run, /api/inpaint/result/{prompt_id}, WS /ws/progress/{prompt_id}
     - models/schemas.py – Pydantic models (RunRequest, RunResponse, ErrorResponse)
     - core/progress.py – in-memory registry (prompt_id → queue) and helpers

3. Config & environment

   - Defaults:
     - COMFY_HOST=127.0.0.1:8188
     - INPUT_DIR=./input
     - WORKFLOW_PATH=./Subject Destination API.json
   - Load from environment/.env (via python-dotenv) with sane fallbacks.

4. Implement ComfyClient

   - Port the core logic from ComfyUI_API_call_example.py, adapted to:
     - Upload three files (object, scene, mask) with correct image_type
     - Patch nodes 18, 37, 293 only
     - Queue prompt and return prompt_id
     - Stream WS messages to a provided queue with graceful error handling
     - On completion, fetch history and parse node 296 outputs

5. Implement routers

   - POST /api/inpaint/run: validate request, upload files, patch workflow, queue prompt, spawn progress task, return prompt_id + URLs
   - WS /ws/progress/{prompt_id}: attach client to queue and forward messages until completion
   - GET /api/inpaint/result/{prompt_id}: retrieve node 296 outputs and return first image (image/png). Optionally: if multiple, return zip and set content-disposition
   - GET /api/health: trivial OK

6. Error handling & status codes

   - 400: invalid inputs (missing filenames), file not found in INPUT_DIR
   - 502/503: ComfyUI unavailable or upload/queue failure
   - 504: progress timeout (no events in N seconds)
   - 404: unknown prompt_id or no outputs
   - Structured ErrorResponse with code and message

7. Testing (manual + minimal unit)

   - Place sample files in /input
   - Start ComfyUI (ensure matching models/assets available for the workflow)
   - uv run fastapi dev app.main:app --reload
   - POST /api/inpaint/run → attach WS to /ws/progress/{prompt_id} → upon completion, GET result
   - Unit-test workflow patching and history parsing for node 296

8. Observability
   - Basic logging of request lifecycle and Comfy errors
   - Optionally, include a /api/inpaint/status/{prompt_id} to query last-known progress without WS

## uv Setup & Commands

- Initialize and add deps:
  - uv init --app
  - uv add fastapi --extra standard
  - uv add requests websocket-client python-dotenv pillow
- Run the dev server:
  - uv run fastapi dev app.main:app --reload
- uv will create and maintain uv.lock, resolve environments, and run commands in the managed venv.

## Error Handling Strategies

- Input validation: ensure three required filenames are provided and exist within INPUT_DIR prior to upload.
- ComfyUI connectivity: catch connection and HTTP errors; return 503 with retry-after hints.
- Upload robustness: verify 2xx from /upload endpoints; include server response on failure.
- Progress watchdog: if no WS events for configurable timeout, send warning/timeout to clients and cancel run (optional) or continue waiting; expose a cancel endpoint in future.
- Completion handling: always reconcile via /history/{prompt_id} to ensure outputs are available; handle cases where SaveImage wrote multiple images or none.
- Security: restrict file access to INPUT_DIR; sanitize filenames; limit payload sizes for future multipart support.

## Notes on Output Retrieval (Node "296")

- After completion, call GET /history/{prompt_id} and locate outputs["296"].
- For each entry in outputs["296"].images, fetch via GET /view with params { filename, subfolder, type }.
- Return the first image by default as image/png; expose query param select=n or an alternative endpoint to fetch specific index if needed.

## Future Enhancements (Optional)

- Support multipart uploads to the backend instead of relying only on /input.
- Add cancellation endpoint to abort a running Comfy prompt.
- Persist progress and results across restarts (e.g., Redis or disk cache).
- Map node IDs to display names from workflow \_meta for nicer progress updates.
