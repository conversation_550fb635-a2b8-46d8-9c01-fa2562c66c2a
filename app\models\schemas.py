from pydantic import BaseModel, Field
from typing import Optional


class RunRequest(BaseModel):
    """Request to start an inpainting workflow"""
    fabric_swatch: str = Field(..., 
        description="Filename in input/ for the fabric swatch image", 
        example="fabric_swatch.png")
    scene_destination: str = Field(..., 
        description="Filename in input/ for the destination scene", 
        example="scene_destination.png")
    curtain_mask: str = Field(..., 
        description="Filename in input/ for the curtain mask", 
        example="curtain_mask.png")
    comfy_server: Optional[str] = Field(None, 
        description="Override ComfyUI server address", 
        example="127.0.0.1:8188")

    class Config:
        json_schema_extra = {
            "example": {
                "fabric_swatch": "fabric_swatch.png", 
                "scene_destination": "scene_destination.png",
                "curtain_mask": "curtain_mask.png",
                "comfy_server": "127.0.0.1:8188"
            }
        }


class RunResponse(BaseModel):
    """Response when workflow is successfully queued"""
    prompt_id: str = Field(..., description="Unique ID for tracking this workflow execution")
    progress_ws: str = Field(..., description="WebSocket URL for real-time progress updates")
    result_url: str = Field(..., description="URL to fetch the generated image when complete")

    class Config:
        json_schema_extra = {
            "example": {
                "prompt_id": "abc123-def456",
                "progress_ws": "/ws/progress/abc123-def456", 
                "result_url": "/api/inpaint/result/abc123-def456"
            }
        }


class ErrorResponse(BaseModel):
    """Error response format"""
    code: str = Field(..., description="Error code")
    message: str = Field(..., description="Human-readable error message")

