# ComfyUI Inpainting Backend Implementation Plan

## Overview
This document outlines the implementation plan for a FastAPI backend server that communicates with ComfyUI to execute inpainting workflows with real-time progress updates.

## Architecture Overview

### System Components
1. **FastAPI Backend Server** - Main API server handling client requests
2. **ComfyUI Service Layer** - Wrapper around ComfyUI API interactions
3. **WebSocket Manager** - Real-time progress updates to clients
4. **File Management** - Handle image uploads and downloads
5. **Workflow Manager** - Dynamic workflow configuration

### Technology Stack
- **Framework**: FastAPI with uvicorn
- **Package Manager**: uv (exclusively)
- **WebSocket**: FastAPI WebSocket support
- **Image Processing**: PIL/Pillow for image handling
- **HTTP Client**: httpx for async requests
- **File Storage**: Local filesystem with organized structure

## API Endpoint Specifications

### Core Endpoints

#### 1. Inpainting Workflow Execution
```
POST /api/v1/inpaint
Content-Type: multipart/form-data

Parameters:
- object_image: File (JPEG/PNG) - Subject to be inpainted
- scene_image: File (JPEG/PNG) - Destination scene
- scene_mask: File (JPEG/PNG) - Mask defining inpainting area
- session_id: str (optional) - For tracking progress

Response:
{
  "session_id": "uuid-string",
  "status": "started",
  "message": "Workflow queued successfully"
}
```

#### 2. Progress Tracking
```
GET /api/v1/progress/{session_id}

Response:
{
  "session_id": "uuid-string",
  "status": "in_progress|completed|failed",
  "progress": {
    "current_step": 5,
    "total_steps": 20,
    "current_node": "110",
    "node_name": "SamplerCustomAdvanced"
  },
  "result_url": "/api/v1/result/{session_id}" (if completed)
}
```

#### 3. WebSocket Real-time Updates
```
WS /ws/{session_id}

Messages:
{
  "type": "progress|executing|completed|error",
  "data": {
    "current_step": 5,
    "total_steps": 20,
    "node": "110",
    "message": "Processing..."
  }
}
```

#### 4. Result Retrieval
```
GET /api/v1/result/{session_id}
Response: Binary image data (JPEG/PNG)
```

#### 5. Health Check
```
GET /api/v1/health
Response: {"status": "healthy", "comfyui_status": "connected"}
```

## Real-time Progress Update Mechanism

### WebSocket Implementation
1. **Connection Management**: Track active WebSocket connections per session
2. **Progress Broadcasting**: Forward ComfyUI progress messages to connected clients
3. **Error Handling**: Graceful disconnection and reconnection support
4. **Message Types**:
   - `progress`: K-sampler step updates
   - `executing`: Current node being processed
   - `completed`: Workflow finished successfully
   - `error`: Error occurred during processing

### Progress Data Flow
```
ComfyUI WebSocket → Backend Service → Session Manager → Client WebSocket
```

## Implementation Tasks

### Phase 1: Project Setup and Core Infrastructure
1. **Initialize uv project structure**
   - Run `uv init --app` to create project
   - Configure `pyproject.toml` with dependencies
   - Set up proper directory structure

2. **Install dependencies using uv**
   ```bash
   uv add fastapi[standard]
   uv add httpx
   uv add pillow
   uv add python-multipart
   uv add websockets
   ```

3. **Create base FastAPI application**
   - Main application setup
   - CORS configuration
   - Basic error handling middleware

### Phase 2: ComfyUI Integration Layer
1. **Adapt ComfyUI service class**
   - Modify existing `ComfyUIService` for async operations
   - Add session management capabilities
   - Implement workflow parameter injection

2. **Workflow configuration manager**
   - Load and parse `Subject Destination API.json`
   - Dynamic parameter injection for nodes 18, 37, 293
   - Validation of required workflow nodes

3. **File upload and management**
   - Secure file upload handling
   - Image validation and processing
   - Temporary file cleanup

### Phase 3: API Endpoints Implementation
1. **Inpainting endpoint (`/api/v1/inpaint`)**
   - File upload validation
   - Session creation and tracking
   - Workflow parameter injection
   - ComfyUI job queuing

2. **Progress tracking endpoint (`/api/v1/progress/{session_id}`)**
   - Session status retrieval
   - Progress data formatting
   - Result URL generation

3. **Result retrieval endpoint (`/api/v1/result/{session_id}`)**
   - Image file serving
   - Proper content-type headers
   - File cleanup after delivery

### Phase 4: Real-time WebSocket Implementation
1. **WebSocket connection manager**
   - Session-based connection tracking
   - Connection lifecycle management
   - Broadcast capabilities

2. **ComfyUI progress integration**
   - WebSocket message parsing
   - Progress data transformation
   - Client notification system

3. **Error handling and recovery**
   - Connection failure handling
   - Automatic reconnection logic
   - Graceful degradation

### Phase 5: Error Handling and Validation
1. **Input validation**
   - Image format validation
   - File size limits
   - Required parameter checking

2. **ComfyUI error handling**
   - Connection failure recovery
   - Workflow execution errors
   - Timeout handling

3. **Session management**
   - Session expiration
   - Cleanup procedures
   - Resource management

## uv-Specific Setup and Deployment

### Development Setup
```bash
# Initialize project
uv init --app comfyui-backend
cd comfyui-backend

# Add dependencies
uv add fastapi[standard] httpx pillow python-multipart websockets

# Development server
uv run fastapi dev app/main.py
```

### Production Deployment
```bash
# Install production dependencies
uv add gunicorn

# Run with gunicorn
uv run gunicorn app.main:app -w 4 -k uvicorn.workers.UvicornWorker --bind 0.0.0.0:8000
```

### Environment Configuration
- Use `.env` files for configuration
- ComfyUI server address configuration
- File storage paths
- Session timeout settings

## Error Handling Strategies

### 1. ComfyUI Connection Errors
- Retry logic with exponential backoff
- Health check endpoint monitoring
- Graceful degradation when ComfyUI unavailable

### 2. File Processing Errors
- Image format validation
- File size limits enforcement
- Temporary file cleanup on errors

### 3. Session Management Errors
- Session timeout handling
- Invalid session ID responses
- Resource cleanup on abandoned sessions

### 4. WebSocket Connection Errors
- Automatic reconnection attempts
- Fallback to HTTP polling
- Connection state management

## Security Considerations
- File upload size limits
- Image format validation
- Session-based access control
- Temporary file cleanup
- Input sanitization

## Performance Optimizations
- Async/await throughout the application
- Connection pooling for ComfyUI requests
- Efficient file handling
- Session cleanup background tasks
- WebSocket connection limits

## Testing Strategy
- Unit tests for core components
- Integration tests with mock ComfyUI
- WebSocket connection testing
- File upload/download testing
- Error scenario testing

## Monitoring and Logging
- Structured logging with session tracking
- Performance metrics collection
- Error rate monitoring
- ComfyUI health monitoring
- WebSocket connection metrics
